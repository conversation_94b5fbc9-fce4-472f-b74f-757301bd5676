/**
 * UI元素编辑器测试页面
 */
import React, { useState } from 'react';
import { Card, Row, Col, Button, Space, message, Typography, Divider, Alert } from 'antd';
import { PlayCircleOutlined, SaveOutlined, ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import UIElementEditor, { UIElementData, UIElementType } from './UIElementEditor';

const { Title, Paragraph, Text } = Typography;

/**
 * UI元素编辑器测试组件
 */
const UIElementEditorTest: React.FC = () => {
  const [uiElementData, setUIElementData] = useState<UIElementData>({
    type: UIElementType.BUTTON,
    name: '测试按钮',
    visible: true,
    interactive: true,
    enabled: true,
    position: { x: 100, y: 100, z: 0 },
    size: { width: 120, height: 40 },
    backgroundColor: '#1890ff',
    borderColor: '#096dd9',
    borderWidth: 1,
    borderRadius: 6,
    opacity: 1.0,
    zIndex: 1,
    textContent: '点击我',
    fontSize: 14,
    fontFamily: 'Arial',
    fontColor: '#ffffff',
    textAlign: 'center',
    padding: { top: 8, right: 16, bottom: 8, left: 16 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    is3D: false,
    onClick: 'console.log("按钮被点击了!");'
  });

  const [savedData, setSavedData] = useState<UIElementData | null>(null);

  // 处理UI元素数据变化
  const handleUIElementChange = (data: UIElementData) => {
    setUIElementData(data);
  };

  // 保存UI元素数据
  const handleSave = () => {
    setSavedData({ ...uiElementData });
    message.success('UI元素数据已保存');
  };

  // 加载保存的数据
  const handleLoad = () => {
    if (savedData) {
      setUIElementData({ ...savedData });
      message.success('已加载保存的UI元素数据');
    } else {
      message.warning('没有保存的数据');
    }
  };

  // 重置为默认数据
  const handleReset = () => {
    const defaultData: UIElementData = {
      type: UIElementType.BUTTON,
      name: '新UI元素',
      visible: true,
      interactive: true,
      enabled: true,
      position: { x: 0, y: 0, z: 0 },
      size: { width: 100, height: 40 },
      backgroundColor: '#f0f0f0',
      borderColor: '#cccccc',
      borderWidth: 1,
      borderRadius: 4,
      opacity: 1.0,
      zIndex: 0,
      textContent: '按钮',
      fontSize: 14,
      fontFamily: 'Arial',
      fontColor: '#333333',
      textAlign: 'center',
      padding: { top: 8, right: 16, bottom: 8, left: 16 },
      margin: { top: 0, right: 0, bottom: 0, left: 0 },
      is3D: false
    };
    setUIElementData(defaultData);
    message.info('已重置为默认数据');
  };

  // 创建预设示例
  const createPreset = (type: UIElementType) => {
    const presets: Record<UIElementType, Partial<UIElementData>> = {
      [UIElementType.BUTTON]: {
        type: UIElementType.BUTTON,
        name: '示例按钮',
        backgroundColor: '#1890ff',
        borderColor: '#096dd9',
        fontColor: '#ffffff',
        textContent: '点击我',
        borderRadius: 6
      },
      [UIElementType.TEXT]: {
        type: UIElementType.TEXT,
        name: '示例文本',
        backgroundColor: 'transparent',
        borderWidth: 0,
        fontColor: '#333333',
        textContent: '这是一段示例文本',
        fontSize: 16
      },
      [UIElementType.PANEL]: {
        type: UIElementType.PANEL,
        name: '示例面板',
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 8,
        size: { width: 300, height: 200 }
      },
      [UIElementType.INPUT]: {
        type: UIElementType.INPUT,
        name: '示例输入框',
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 4,
        textContent: '请输入内容...'
      },
      [UIElementType.IMAGE]: {
        type: UIElementType.IMAGE,
        name: '示例图像',
        backgroundColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        size: { width: 200, height: 150 }
      },
      [UIElementType.WINDOW]: {
        type: UIElementType.WINDOW,
        name: '示例窗口',
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 8,
        size: { width: 400, height: 300 }
      },
      [UIElementType.SLIDER]: {
        type: UIElementType.SLIDER,
        name: '示例滑块',
        backgroundColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 4,
        size: { width: 200, height: 20 }
      },
      [UIElementType.CHECKBOX]: {
        type: UIElementType.CHECKBOX,
        name: '示例复选框',
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 2,
        size: { width: 16, height: 16 }
      },
      [UIElementType.DROPDOWN]: {
        type: UIElementType.DROPDOWN,
        name: '示例下拉框',
        backgroundColor: '#ffffff',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        borderRadius: 4,
        textContent: '请选择...'
      }
    };

    const preset = presets[type];
    setUIElementData({ ...uiElementData, ...preset });
    message.success(`已应用${type}预设`);
  };

  return (
    <div style={{ padding: 24, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题和说明 */}
      <div style={{ marginBottom: 24, textAlign: 'center' }}>
        <Title level={2}>UI元素编辑器演示</Title>
        <Paragraph>
          这是一个功能完整的UI元素编辑器，支持创建、编辑和预览各种UI组件。
          您可以通过左侧的编辑器修改UI元素属性，右侧会实时显示当前数据。
        </Paragraph>
        <Alert
          message="功能说明"
          description="支持9种UI元素类型，包含完整的属性编辑、预设系统、实时预览等功能。点击'选择预设'按钮可以快速应用预设样式。"
          type="info"
          icon={<InfoCircleOutlined />}
          showIcon
          style={{ marginBottom: 16 }}
        />
      </div>

      <Row gutter={24}>
        <Col span={14}>
          <Card
            title="UI元素编辑器"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSave}
                >
                  保存
                </Button>
                <Button
                  icon={<PlayCircleOutlined />}
                  onClick={handleLoad}
                  disabled={!savedData}
                >
                  加载
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                >
                  重置
                </Button>
              </Space>
            }
          >
            <UIElementEditor
              data={uiElementData}
              onChange={handleUIElementChange}
              showPreview={true}
              previewContainerId="test-ui-preview"
            />
          </Card>
        </Col>

        <Col span={10}>
          <Card title="快速预设" style={{ marginBottom: 16 }}>
            <Paragraph style={{ fontSize: 12, color: '#666' }}>
              点击下方按钮快速应用不同类型的UI元素预设
            </Paragraph>
            <Space wrap>
              {Object.values(UIElementType).map(type => (
                <Button
                  key={type}
                  size="small"
                  onClick={() => createPreset(type)}
                  style={{ marginBottom: 4 }}
                >
                  {type}
                </Button>
              ))}
            </Space>
          </Card>

          <Card title="当前元素信息" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>类型:</Text> <Text code>{uiElementData.type}</Text>
              </div>
              <div>
                <Text strong>名称:</Text> <Text>{uiElementData.name}</Text>
              </div>
              <div>
                <Text strong>尺寸:</Text>
                <Text code>{uiElementData.size?.width} × {uiElementData.size?.height}</Text>
              </div>
              <div>
                <Text strong>位置:</Text>
                <Text code>
                  ({uiElementData.position?.x}, {uiElementData.position?.y}
                  {uiElementData.is3D && `, ${uiElementData.position?.z}`})
                </Text>
              </div>
              <div>
                <Text strong>可见:</Text>
                <Text type={uiElementData.visible ? 'success' : 'danger'}>
                  {uiElementData.visible ? '是' : '否'}
                </Text>
              </div>
              <div>
                <Text strong>可交互:</Text>
                <Text type={uiElementData.interactive ? 'success' : 'danger'}>
                  {uiElementData.interactive ? '是' : '否'}
                </Text>
              </div>
            </Space>
          </Card>

          <Card title="完整数据" size="small">
            <pre style={{
              fontSize: 11,
              maxHeight: 300,
              overflow: 'auto',
              backgroundColor: '#f8f9fa',
              padding: 12,
              borderRadius: 4,
              border: '1px solid #e9ecef'
            }}>
              {JSON.stringify(uiElementData, null, 2)}
            </pre>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default UIElementEditorTest;
