/**
 * UI元素编辑器
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Select,
  InputNumber,
  Input,
  Switch,
  Button,
  Tabs,
  Space,
  Divider,
  ColorPicker,
  Slider,
  Row,
  Col,
  message,
  Tooltip
} from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  ReloadOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import engineService from '../../services/EngineService';
import UIPresetSelector from './UIPresetSelector';
import uiPresetManager from './UIPresetManager';
import type { UIPreset } from './UIPresetManager';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * UI元素类型枚举
 */
export enum UIElementType {
  BUTTON = 'button',
  TEXT = 'text',
  IMAGE = 'image',
  INPUT = 'input',
  PANEL = 'panel',
  WINDOW = 'window',
  SLIDER = 'slider',
  CHECKBOX = 'checkbox',
  DROPDOWN = 'dropdown'
}

/**
 * UI元素属性接口
 */
export interface UIElementData {
  // 基本属性
  id?: string;
  type: UIElementType;
  name?: string;
  visible?: boolean;
  interactive?: boolean;

  // 位置和尺寸
  position?: {
    x: number;
    y: number;
    z?: number;
  };
  size?: {
    width: number;
    height: number;
  };

  // 样式属性
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  opacity?: number;
  zIndex?: number;

  // 文本属性
  textContent?: string;
  fontSize?: number;
  fontFamily?: string;
  fontColor?: string;
  textAlign?: 'left' | 'center' | 'right';

  // 布局属性
  padding?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };

  // 3D属性
  is3D?: boolean;
  billboardMode?: string;

  // 事件属性
  onClick?: string;
  onHover?: string;
  onChange?: string;

  // 其他属性
  enabled?: boolean;
  userData?: any;
}

/**
 * UI元素编辑器属性
 */
interface UIElementEditorProps {
  /** 组件数据 */
  data?: UIElementData;
  /** 数据更新回调 */
  onChange?: (data: UIElementData) => void;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 预览容器ID */
  previewContainerId?: string;
}

/**
 * UI元素编辑器组件
 */
const UIElementEditor: React.FC<UIElementEditorProps> = ({
  data,
  onChange,
  showPreview = true,
  previewContainerId = 'ui-preview-container'
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [previewVisible, setPreviewVisible] = useState(showPreview);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [presetSelectorVisible, setPresetSelectorVisible] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);

  // 默认数据
  const defaultData: UIElementData = {
    type: UIElementType.BUTTON,
    name: '新UI元素',
    visible: true,
    interactive: true,
    enabled: true,
    position: { x: 0, y: 0, z: 0 },
    size: { width: 100, height: 40 },
    backgroundColor: '#f0f0f0',
    borderColor: '#cccccc',
    borderWidth: 1,
    borderRadius: 4,
    opacity: 1.0,
    zIndex: 0,
    textContent: '按钮',
    fontSize: 14,
    fontFamily: 'Arial',
    fontColor: '#333333',
    textAlign: 'center',
    padding: { top: 8, right: 16, bottom: 8, left: 16 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    is3D: false,
    ...data
  };

  const [currentData, setCurrentData] = useState<UIElementData>(defaultData);

  useEffect(() => {
    if (data) {
      setCurrentData({ ...defaultData, ...data });
      form.setFieldsValue({ ...defaultData, ...data });
    }
  }, [data, form]);

  // 处理数据变化
  const handleDataChange = (field: string, value: any) => {
    const newData = { ...currentData, [field]: value };
    setCurrentData(newData);

    if (onChange) {
      onChange(newData);
    }

    // 实时更新预览
    if (previewVisible) {
      updatePreview(newData);
    }
  };

  // 处理嵌套对象数据变化
  const handleNestedDataChange = (parentField: string, field: string, value: any) => {
    const newData = {
      ...currentData,
      [parentField]: {
        ...currentData[parentField as keyof UIElementData],
        [field]: value
      }
    };
    setCurrentData(newData);

    if (onChange) {
      onChange(newData);
    }

    // 实时更新预览
    if (previewVisible) {
      updatePreview(newData);
    }
  };

  // 更新预览
  const updatePreview = async (elementData: UIElementData) => {
    if (!previewRef.current) return;

    setIsPreviewLoading(true);
    try {
      // 通过引擎服务创建或更新UI元素
      if (elementData.id) {
        engineService.updateUI(elementData.id, elementData);
      } else {
        const uiElement = engineService.createUI(elementData.type, elementData);
        if (uiElement && uiElement.id) {
          handleDataChange('id', uiElement.id);
        }
      }
    } catch (error) {
      console.error('预览更新失败:', error);
      message.error('预览更新失败');
    } finally {
      setIsPreviewLoading(false);
    }
  };

  // 切换预览可见性
  const togglePreview = () => {
    setPreviewVisible(!previewVisible);
    if (!previewVisible) {
      updatePreview(currentData);
    }
  };

  // 保存UI元素
  const handleSave = () => {
    try {
      if (onChange) {
        onChange(currentData);
      }
      message.success('UI元素已保存');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 重置UI元素
  const handleReset = () => {
    const resetData = { ...defaultData };
    setCurrentData(resetData);
    form.setFieldsValue(resetData);
    if (onChange) {
      onChange(resetData);
    }
    message.info('已重置为默认值');
  };

  // 应用预设
  const handleApplyPreset = (preset: UIPreset) => {
    const newData = { ...currentData, ...preset.data };
    setCurrentData(newData);
    form.setFieldsValue(newData);
    if (onChange) {
      onChange(newData);
    }
    message.success(`已应用预设: ${preset.name}`);
  };

  // 打开预设选择器
  const handleOpenPresetSelector = () => {
    setPresetSelectorVisible(true);
  };

  // 关闭预设选择器
  const handleClosePresetSelector = () => {
    setPresetSelectorVisible(false);
  };

  // 获取UI元素类型选项
  const getUITypeOptions = () => {
    return Object.values(UIElementType).map(type => ({
      label: getUITypeLabel(type),
      value: type
    }));
  };

  // 获取UI元素类型标签
  const getUITypeLabel = (type: UIElementType): string => {
    const labels = {
      [UIElementType.BUTTON]: '按钮',
      [UIElementType.TEXT]: '文本',
      [UIElementType.IMAGE]: '图像',
      [UIElementType.INPUT]: '输入框',
      [UIElementType.PANEL]: '面板',
      [UIElementType.WINDOW]: '窗口',
      [UIElementType.SLIDER]: '滑块',
      [UIElementType.CHECKBOX]: '复选框',
      [UIElementType.DROPDOWN]: '下拉框'
    };
    return labels[type] || type;
  };

  // 渲染基本属性面板
  const renderBasicPanel = () => (
    <div className="ui-editor-panel">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="类型" name="type">
            <Select
              value={currentData.type}
              onChange={(value) => handleDataChange('type', value)}
              options={getUITypeOptions()}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="名称" name="name">
            <Input
              value={currentData.name}
              onChange={(e) => handleDataChange('name', e.target.value)}
              placeholder="输入UI元素名称"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="可见" name="visible" valuePropName="checked">
            <Switch
              checked={currentData.visible}
              onChange={(checked) => handleDataChange('visible', checked)}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="可交互" name="interactive" valuePropName="checked">
            <Switch
              checked={currentData.interactive}
              onChange={(checked) => handleDataChange('interactive', checked)}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="启用" name="enabled" valuePropName="checked">
            <Switch
              checked={currentData.enabled}
              onChange={(checked) => handleDataChange('enabled', checked)}
            />
          </Form.Item>
        </Col>
      </Row>

      {(currentData.type === UIElementType.BUTTON ||
        currentData.type === UIElementType.TEXT) && (
        <Form.Item label="文本内容" name="textContent">
          <Input
            value={currentData.textContent}
            onChange={(e) => handleDataChange('textContent', e.target.value)}
            placeholder="输入文本内容"
          />
        </Form.Item>
      )}

      <Form.Item label="3D模式" name="is3D" valuePropName="checked">
        <Switch
          checked={currentData.is3D}
          onChange={(checked) => handleDataChange('is3D', checked)}
        />
      </Form.Item>

      <Divider />

      <Form.Item label="预设">
        <Button
          icon={<AppstoreOutlined />}
          onClick={handleOpenPresetSelector}
          block
        >
          选择预设
        </Button>
      </Form.Item>
    </div>
  );

  // 渲染位置和尺寸面板
  const renderTransformPanel = () => (
    <div className="ui-editor-panel">
      <h4>位置</h4>
      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="X" name={['position', 'x']}>
            <InputNumber
              value={currentData.position?.x}
              onChange={(value) => handleNestedDataChange('position', 'x', value || 0)}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Y" name={['position', 'y']}>
            <InputNumber
              value={currentData.position?.y}
              onChange={(value) => handleNestedDataChange('position', 'y', value || 0)}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        {currentData.is3D && (
          <Col span={8}>
            <Form.Item label="Z" name={['position', 'z']}>
              <InputNumber
                value={currentData.position?.z}
                onChange={(value) => handleNestedDataChange('position', 'z', value || 0)}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        )}
      </Row>

      <Divider />

      <h4>尺寸</h4>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="宽度" name={['size', 'width']}>
            <InputNumber
              value={currentData.size?.width}
              onChange={(value) => handleNestedDataChange('size', 'width', value || 100)}
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="高度" name={['size', 'height']}>
            <InputNumber
              value={currentData.size?.height}
              onChange={(value) => handleNestedDataChange('size', 'height', value || 40)}
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="透明度" name="opacity">
            <Slider
              value={currentData.opacity}
              onChange={(value) => handleDataChange('opacity', value)}
              min={0}
              max={1}
              step={0.01}
              marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="层级" name="zIndex">
            <InputNumber
              value={currentData.zIndex}
              onChange={(value) => handleDataChange('zIndex', value || 0)}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );

  // 渲染样式面板
  const renderStylePanel = () => (
    <div className="ui-editor-panel">
      <h4>颜色</h4>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="背景色" name="backgroundColor">
            <ColorPicker
              value={currentData.backgroundColor}
              onChange={(color) => handleDataChange('backgroundColor', color.toHexString())}
              showText
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="边框色" name="borderColor">
            <ColorPicker
              value={currentData.borderColor}
              onChange={(color) => handleDataChange('borderColor', color.toHexString())}
              showText
            />
          </Form.Item>
        </Col>
      </Row>

      <h4>边框</h4>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="边框宽度" name="borderWidth">
            <InputNumber
              value={currentData.borderWidth}
              onChange={(value) => handleDataChange('borderWidth', value || 0)}
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="圆角" name="borderRadius">
            <InputNumber
              value={currentData.borderRadius}
              onChange={(value) => handleDataChange('borderRadius', value || 0)}
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>

      {(currentData.type === UIElementType.BUTTON ||
        currentData.type === UIElementType.TEXT) && (
        <>
          <Divider />
          <h4>文本样式</h4>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="字体大小" name="fontSize">
                <InputNumber
                  value={currentData.fontSize}
                  onChange={(value) => handleDataChange('fontSize', value || 14)}
                  min={8}
                  max={72}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="字体" name="fontFamily">
                <Select
                  value={currentData.fontFamily}
                  onChange={(value) => handleDataChange('fontFamily', value)}
                >
                  <Option value="Arial">Arial</Option>
                  <Option value="Helvetica">Helvetica</Option>
                  <Option value="Times New Roman">Times New Roman</Option>
                  <Option value="Courier New">Courier New</Option>
                  <Option value="微软雅黑">微软雅黑</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="文本对齐" name="textAlign">
                <Select
                  value={currentData.textAlign}
                  onChange={(value) => handleDataChange('textAlign', value)}
                >
                  <Option value="left">左对齐</Option>
                  <Option value="center">居中</Option>
                  <Option value="right">右对齐</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="文本颜色" name="fontColor">
            <ColorPicker
              value={currentData.fontColor}
              onChange={(color) => handleDataChange('fontColor', color.toHexString())}
              showText
            />
          </Form.Item>
        </>
      )}
    </div>
  );

  // 渲染布局面板
  const renderLayoutPanel = () => (
    <div className="ui-editor-panel">
      <h4>内边距</h4>
      <Row gutter={8}>
        <Col span={6}>
          <Form.Item label="上" name={['padding', 'top']}>
            <InputNumber
              value={currentData.padding?.top}
              onChange={(value) => handleNestedDataChange('padding', 'top', value || 0)}
              min={0}
              size="small"
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="右" name={['padding', 'right']}>
            <InputNumber
              value={currentData.padding?.right}
              onChange={(value) => handleNestedDataChange('padding', 'right', value || 0)}
              min={0}
              size="small"
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="下" name={['padding', 'bottom']}>
            <InputNumber
              value={currentData.padding?.bottom}
              onChange={(value) => handleNestedDataChange('padding', 'bottom', value || 0)}
              min={0}
              size="small"
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="左" name={['padding', 'left']}>
            <InputNumber
              value={currentData.padding?.left}
              onChange={(value) => handleNestedDataChange('padding', 'left', value || 0)}
              min={0}
              size="small"
            />
          </Form.Item>
        </Col>
      </Row>

      <h4>外边距</h4>
      <Row gutter={8}>
        <Col span={6}>
          <Form.Item label="上" name={['margin', 'top']}>
            <InputNumber
              value={currentData.margin?.top}
              onChange={(value) => handleNestedDataChange('margin', 'top', value || 0)}
              size="small"
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="右" name={['margin', 'right']}>
            <InputNumber
              value={currentData.margin?.right}
              onChange={(value) => handleNestedDataChange('margin', 'right', value || 0)}
              size="small"
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="下" name={['margin', 'bottom']}>
            <InputNumber
              value={currentData.margin?.bottom}
              onChange={(value) => handleNestedDataChange('margin', 'bottom', value || 0)}
              size="small"
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="左" name={['margin', 'left']}>
            <InputNumber
              value={currentData.margin?.left}
              onChange={(value) => handleNestedDataChange('margin', 'left', value || 0)}
              size="small"
            />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );

  // 渲染事件面板
  const renderEventsPanel = () => (
    <div className="ui-editor-panel">
      <Form.Item label="点击事件" name="onClick">
        <Input.TextArea
          value={currentData.onClick}
          onChange={(e) => handleDataChange('onClick', e.target.value)}
          placeholder="输入点击事件处理代码"
          rows={3}
        />
      </Form.Item>

      <Form.Item label="悬停事件" name="onHover">
        <Input.TextArea
          value={currentData.onHover}
          onChange={(e) => handleDataChange('onHover', e.target.value)}
          placeholder="输入悬停事件处理代码"
          rows={3}
        />
      </Form.Item>

      {(currentData.type === UIElementType.INPUT ||
        currentData.type === UIElementType.SLIDER ||
        currentData.type === UIElementType.CHECKBOX ||
        currentData.type === UIElementType.DROPDOWN) && (
        <Form.Item label="值变化事件" name="onChange">
          <Input.TextArea
            value={currentData.onChange}
            onChange={(e) => handleDataChange('onChange', e.target.value)}
            placeholder="输入值变化事件处理代码"
            rows={3}
          />
        </Form.Item>
      )}
    </div>
  );

  return (
    <Card
      title="UI元素编辑器"
      size="small"
      extra={
        <Space>
          <Tooltip title={previewVisible ? '隐藏预览' : '显示预览'}>
            <Button
              type="text"
              icon={previewVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={togglePreview}
            />
          </Tooltip>
          <Tooltip title="保存">
            <Button
              type="text"
              icon={<SaveOutlined />}
              onClick={handleSave}
            />
          </Tooltip>
          <Tooltip title="重置">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={handleReset}
            />
          </Tooltip>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={currentData}
        size="small"
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
          <TabPane tab="基本" key="basic" icon={<SettingOutlined />}>
            {renderBasicPanel()}
          </TabPane>

          <TabPane tab="变换" key="transform">
            {renderTransformPanel()}
          </TabPane>

          <TabPane tab="样式" key="style">
            {renderStylePanel()}
          </TabPane>

          <TabPane tab="布局" key="layout">
            {renderLayoutPanel()}
          </TabPane>

          <TabPane tab="事件" key="events">
            {renderEventsPanel()}
          </TabPane>
        </Tabs>
      </Form>

      {previewVisible && (
        <div className="ui-preview-section" style={{ marginTop: 16 }}>
          <Divider>预览</Divider>
          <div
            ref={previewRef}
            id={previewContainerId}
            className="ui-preview-container"
            style={{
              border: '1px dashed #d9d9d9',
              borderRadius: 4,
              padding: 16,
              minHeight: 100,
              backgroundColor: '#fafafa',
              position: 'relative'
            }}
          >
            {isPreviewLoading && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                color: '#999'
              }}>
                <PlayCircleOutlined /> 加载预览中...
              </div>
            )}
            {!isPreviewLoading && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                color: '#999'
              }}>
                UI元素预览区域
              </div>
            )}
          </div>
        </div>
      )}

      {/* 预设选择器 */}
      <UIPresetSelector
        visible={presetSelectorVisible}
        onClose={handleClosePresetSelector}
        onSelect={handleApplyPreset}
        filterType={currentData.type}
        title="选择UI预设"
      />
    </Card>
  );
};

export default UIElementEditor;
